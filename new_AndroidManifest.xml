<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="87"
    android:versionName="0.5.6.2"
    android:compileSdkVersion="35"
    android:compileSdkVersionCodename="15"
    android:requiredSplitTypes="base__abi,base__density"
    android:splitTypes=""
    package="com.bible.holy.bible.for.women"
    platformBuildVersionCode="35"
    platformBuildVersionName="15">
    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="com.android.vending.CHECK_LICENSE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="com.android.vending.BILLING"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW"/>
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT"/>
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS"/>
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS"/>
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS"/>
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"
        android:minSdkVersion="34"/>
    <supports-screens
        android:anyDensity="true"
        android:smallScreens="true"
        android:normalScreens="true"
        android:largeScreens="true"
        android:resizeable="true"
        android:xlargeScreens="true"/>
    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider"/>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <category android:name="android.intent.category.BROWSABLE"/>
            <data android:scheme="https"/>
        </intent>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.INSERT"/>
            <data android:mimeType="vnd.android.cursor.dir/event"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
            <data android:scheme="sms"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.DIAL"/>
            <data android:path="tel:"/>
        </intent>
        <package android:name="com.facebook.katana"/>
        <intent>
            <action android:name="com.android.vending.billing.InAppBillingService.BIND"/>
        </intent>
        <package android:name="com.pubmatic.openwrapapp"/>
    </queries>
    <uses-permission android:name="android.permission.READ_BASIC_PHONE_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION"/>
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE"/>
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE"/>
    <permission
        android:name="com.bible.holy.bible.for.women.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature"/>
    <uses-permission android:name="com.bible.holy.bible.for.women.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"/>
    <application
        android:theme="@style/j"
        android:label="@string/i_"
        android:icon="@mipmap/ic_launcher"
        android:name="com.pairip.application.Application"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        android:supportsRtl="true"
        android:extractNativeLibs="false"
        android:networkSecurityConfig="@xml/g"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:localeConfig="@xml/f">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false"/>
        <activity
            android:theme="@style/hr"
            android:name="com.offline.bible.ui.splash.LaunchActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <action android:name="android.intent.action.VIEW"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="https"
                    android:host="bibliacomigo.page.link"/>
                <data
                    android:scheme="https"
                    android:host="bibleforwomen.go.link"/>
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="BFW"/>
            </intent-filter>
        </activity>
        <activity-alias
            android:icon="@mipmap/ic_launcher_6"
            android:name="com.offline.bible.Icon6"
            android:enabled="false"
            android:exported="true"
            android:targetActivity="com.offline.bible.ui.splash.LaunchActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:icon="@mipmap/ic_launcher_5"
            android:name="com.offline.bible.Icon5"
            android:enabled="false"
            android:exported="true"
            android:targetActivity="com.offline.bible.ui.splash.LaunchActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:icon="@mipmap/ic_launcher_4"
            android:name="com.offline.bible.Icon4"
            android:enabled="false"
            android:exported="true"
            android:targetActivity="com.offline.bible.ui.splash.LaunchActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:icon="@mipmap/ic_launcher_3"
            android:name="com.offline.bible.Icon3"
            android:enabled="false"
            android:exported="true"
            android:targetActivity="com.offline.bible.ui.splash.LaunchActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:icon="@mipmap/ic_launcher_2"
            android:name="com.offline.bible.Icon2"
            android:enabled="false"
            android:exported="true"
            android:targetActivity="com.offline.bible.ui.splash.LaunchActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:icon="@mipmap/ic_launcher_1"
            android:name="com.offline.bible.Icon1"
            android:enabled="false"
            android:exported="true"
            android:targetActivity="com.offline.bible.ui.splash.LaunchActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:icon="@mipmap/ic_launcher"
            android:name="com.offline.bible.IconDefault"
            android:enabled="true"
            android:exported="true"
            android:targetActivity="com.offline.bible.ui.splash.LaunchActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity-alias>
        <activity
            android:name="com.offline.bible.ui.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.offline.bible.ui.user.RegisterGuiActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.user.LoginActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden"/>
        <activity
            android:name="com.offline.bible.ui.user.RegisterActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden"/>
        <activity
            android:name="com.offline.bible.ui.user.ForgetPasswordActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.MyNotesActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.NoteEditActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.me.MyMessageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.ReadRecentHistoryActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.ShareSuccessActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:name="com.offline.bible.ui.user.ThirdLoginActivity"
            android:configChanges="screenSize|screenLayout|orientation|keyboardHidden|keyboard"
            android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity
            android:name="com.offline.bible.ui.WebViewActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.ShareImageCropActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.ShareImageEditActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.voice.VoicePlayingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.voice.PassagesListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.voice.PlayListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.voice.PlayListDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.TopicListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.TopicMedalActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.ReadMarkSuccessActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.ReadMarkSuccess2Activity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.me.PresentNoAdActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.me.PresentActiveSuccessActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.settings.NotificationSettingActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.offline.bible.ui.settings.NotificationSettingV2Activity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.me.MultiEditionActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.news.NewsDetailActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity
            android:name="com.offline.bible.ui.news.PrayActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.checkin.CheckInActivityNew"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.checkin.CheckinPointDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.checkin.CheckinPointGetActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.checkin.ThoughtsActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.settings.ReadNoteSettingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.voice.VoiceCompleteSuccessActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan.PlanDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan.PlanAlarmSettingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.EncourageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.EncouragePrayActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.EncouragePray2Activity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz.QuizHomeActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz.QuizDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz.QuizLoadDataActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz.QuizStartCountdownActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz.QuizEncourageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz2.QuizHomeActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz2.QuizDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz2.QuizLoadDataActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz2.QuizStartCountdownActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz2.QuizEncourageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz3.activity.QuizPuzzleMainActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz3.activity.QuizDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz3.activity.QuizEncourageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz3.activity.QuizMedalActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.quiz3.activity.QuizPassedLevelActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.crossword.CrossWordLoadDataActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.crossword.CrossWordHomeActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.crossword.CrossWordDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.removead.RemoveAdActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.removead.RemovedAdActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.ui.removead.RemoveAdDialogActivity"
            android:launchMode="singleTop"
            android:screenOrientation="nosensor"/>
        <activity
            android:name="com.offline.bible.ui.recruit.RecruitActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.community.CommunityActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.community.CommunityInfoActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.community.CommunityShareActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.overlay.OverlayGuideActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.overlay.OverlayGuideV2Activity"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.ui.overlay.OverlayWindowGuideActivity"
            android:screenOrientation="nosensor"/>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.ui.overlay.OverlayWindowGuide2Activity"
            android:screenOrientation="nosensor"/>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.ui.dialog.DemandFeedbackDialog"
            android:screenOrientation="nosensor"/>
        <activity
            android:name="com.offline.bible.ui.AboutUsActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan.PlanTipsActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan.PlanCompletedListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan.PlanListWithClassificationActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.PagerDiscoverActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.ReportIssueActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.GospelAndPsalmsDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.PrayDetailActivityWaterfallC"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.ui.home.PrayDetailVerseImageActivity"
            android:screenOrientation="nosensor"/>
        <activity
            android:name="com.offline.bible.ui.home.advent.PrayDetailActivityForAdvent"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.advent.PrayCandleActivityForAdvent"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan.v2.PlanReadActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan.v2.PlanFinishEncourageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.dialog.ReminderTipsV2Dialog"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.collect.CollectVerseActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.me.MultiEditionUploadInfoActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.user.DeleteAccountActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.survey.SurveyNewFunctionActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"/>
        <activity
            android:name="com.offline.bible.ui.survey.SurveyEncourageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.more.ThemeSettingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.readIndex.ReadIndexActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.ui.plan14.Plan14Activity"
            android:screenOrientation="nosensor"/>
        <activity
            android:name="com.offline.bible.ui.plan14.Plan14DetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.plan14.Plan14EncourageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.more.UserSettingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.note.BibleNoteListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.note.BibleNoteDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.note.BibleNoteEditActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.highlight.BibleHighlightListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.bookmarks.BibleBookMarksListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.read.ReadBibleActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.dailyverse.DailyVerseAndGospelActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:name="com.offline.bible.ui.overlay.NotificationShortcutWindow"
            android:exported="true"
            android:screenOrientation="nosensor">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.offline.bible.ui.read.MarkReadProgressActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.faithAchievement.AchievementListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.faithAchievement.AppIconListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.help.HelpCenterActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.help.HelpCenterDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.help.FeedbackEditActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.help.FeedbackListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.help.FeedbackDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.help.FeedbackImageActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.manager.admanager.launchad.WelcomeNativeAdActivity"
            android:screenOrientation="nosensor"/>
        <activity
            android:name="com.offline.bible.ui.aigc.view.WritePrayerActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"/>
        <activity
            android:name="com.offline.bible.ui.search.theme.ThemePrayTopicActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.search.theme.ThemePrayListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.search.theme.ThemePrayDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.DxdAudioPlayingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:name="com.offline.bible.ui.overlay.OverlayWindowActivity"
            android:exported="true"
            android:taskAffinity="com.offline.bible.overlay.notify"
            android:excludeFromRecents="true"
            android:launchMode="singleTask"
            android:screenOrientation="nosensor">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
            </intent-filter>
        </activity>
        <service
            android:name="com.offline.bible.voice.VoiceService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback"/>
        <service
            android:name="com.offline.bible.service.DownloadService"
            android:exported="false"/>
        <receiver
            android:name="com.offline.bible.receiver.TimingPushBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="action_timing_push_receive"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.REBOOT"/>
            </intent-filter>
        </receiver>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.FileProvider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/c"/>
        </provider>
        <service
            android:name="com.offline.bible.FirebaseNotifyService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <activity
            android:theme="@style/a3y"
            android:label="@string/i8"
            android:name="com.facebook.FacebookActivity"
            android:configChanges="screenSize|screenLayout|orientation|keyboardHidden|keyboard"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.facebook.ads.AudienceNetworkActivity"
            android:exported="false"
            android:configChanges="smallestScreenSize|screenSize|screenLayout|orientation|keyboardHidden"
            android:hardwareAccelerated="true"/>
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="@string/vf"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="fbconnect"
                    android:host="cct.com.bible.holy.bible.for.women"/>
            </intent-filter>
        </activity>
        <activity
            android:theme="@style/uf"
            android:name="com.offline.bible.permission.OverlayPermission"
            android:exported="false"
            android:stateNotNeeded="true"
            android:launchMode="singleTask"
            android:screenOrientation="sensor"
            android:configChanges="fontScale|layoutDirection|density|screenSize|screenLayout|orientation|navigation|keyboardHidden|keyboard|locale"/>
        <activity
            android:name="com.offline.bible.ui.home.PersonalizeDailyTipsActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.SabbathMeditationPlayingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.EncourageGospelActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.aiverse.ui.AiVerseMainActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.aiverse.ui.AiVerseJarMainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"/>
        <activity
            android:name="com.offline.bible.ui.home.aiverse.ui.AiVerseDetailActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.aiverse.ui.AiVerseHistoryActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.aiverse.ui.AiVersePermissionActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.aiverse.ui.VerseOfYouActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"/>
        <activity
            android:name="com.offline.bible.ui.removead.v5.RemoveAdLoadingActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.removead.v5.RemoveAdPreActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.removead.v5.RemoveAdTrialActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.TestMediaActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.garden.ui.FaithGardenHomeActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.garden.ui.FaithGardenChoosePlantActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.garden.ui.FaithGardenPlantListActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.weekly.recap.WeeklyRecapActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.weekly.report.WeeklyReportActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.offline.bible.ui.home.TestMediaActivity"
            android:screenOrientation="portrait"/>
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/v_"/>
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/va"/>
        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:exported="true"
            android:authorities="com.facebook.app.FacebookContentProvider387574557255685"/>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_notify_icon"/>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/b0"/>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/t4"/>
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/fk"/>
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/i"/>
        <meta-data
            android:name="design_width_in_dp"
            android:value="360"/>
        <meta-data
            android:name="design_height_in_dp"
            android:value="640"/>
        <service
            android:name="com.offline.bible.service.sync.service.AuthenticatorService"
            android:exported="false">
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator"/>
            </intent-filter>
            <meta-data
                android:name="android.accounts.AccountAuthenticator"
                android:resource="@xml/a"/>
        </service>
        <service
            android:name="com.offline.bible.service.sync.service.SyncService"
            android:exported="true">
            <intent-filter>
                <action android:name="android.content.SyncAdapter"/>
            </intent-filter>
            <meta-data
                android:name="android.content.SyncAdapter"
                android:resource="@xml/m"/>
        </service>
        <provider
            android:name="com.offline.bible.service.sync.provider.StubProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.datasync.provider"
            android:syncable="true"/>
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true"/>
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true"/>
        <activity
            android:name="com.qonversion.android.sdk.automations.mvp.ScreenActivity"
            android:screenOrientation="portrait"/>
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:exported="false"
            android:directBootAware="true"/>
        <activity
            android:theme="@android:style/Theme.Translucent"
            android:name="com.google.android.libraries.ads.mobile.sdk.common.AdActivity"
            android:exported="false"
            android:configChanges="smallestScreenSize|screenSize|uiMode|screenLayout|orientation|keyboardHidden|keyboard"/>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.androidx-startup">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="android.view.ProcessLifecycleInitializer"
                android:value="androidx.startup"/>
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup"/>
        </provider>
        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:enabled="@bool/d"
            android:exported="false"
            android:directBootAware="false"/>
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="@bool/f"
            android:exported="true"
            android:directBootAware="false"/>
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:enabled="@bool/e"
            android:exported="false"
            android:directBootAware="false"/>
        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:enabled="true"
            android:exported="false"
            android:directBootAware="false"/>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED"/>
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY"/>
                <action android:name="android.intent.action.BATTERY_LOW"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW"/>
                <action android:name="android.intent.action.DEVICE_STORAGE_OK"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:enabled="false"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.TIME_SET"/>
                <action android:name="android.intent.action.TIMEZONE_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:enabled="@bool/d"
            android:exported="false"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS"/>
            </intent-filter>
        </receiver>
        <activity android:name="com.facebook.CustomTabMainActivity"/>
        <provider
            android:name="com.facebook.internal.FacebookInitProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.FacebookInitProvider"/>
        <receiver
            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED"/>
            </intent-filter>
        </receiver>
        <provider
            android:name="com.google.firebase.perf.provider.FirebasePerfProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.firebaseperfprovider"
            android:initOrder="101"/>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:exported="false"
            android:directBootAware="true">
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.dynamiclinks.internal.FirebaseDynamicLinkRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar"/>
        </service>
        <meta-data
            android:name="com.google.android.play.billingclient.version"
            android:value="6.1.0"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.android.billingclient.api.ProxyBillingActivity"
            android:exported="false"
            android:configChanges="screenSize|screenLayout|orientation|keyboardHidden|keyboard"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:exported="false"
            android:excludeFromRecents="true"/>
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:exported="true"
            android:visibleToInstantApps="true"/>
        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="true"
            android:exported="false"/>
        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:permission="com.google.android.c2dm.permission.SEND"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
            </intent-filter>
        </receiver>
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:exported="false"
            android:directBootAware="true">
            <intent-filter android:priority="-500">
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
            android:exported="true"
            android:excludeFromRecents="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="genericidp"
                    android:host="firebase.auth"
                    android:path="/"/>
            </intent-filter>
        </activity>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
            android:exported="true"
            android:excludeFromRecents="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="recaptcha"
                    android:host="firebase.auth"
                    android:path="/"/>
            </intent-filter>
        </activity>
        <meta-data
            android:name="com.bumptech.glide.integration.webp.WebpGlideModule"
            android:value="GlideModule"/>
        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/d"/>
        <activity
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"/>
        <service
            android:name="com.google.firebase.sessions.SessionLifecycleService"
            android:enabled="true"
            android:exported="false"/>
        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.firebaseinitprovider"
            android:initOrder="100"
            android:directBootAware="true"/>
        <uses-library
            android:name="androidx.window.extensions"
            android:required="false"/>
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false"/>
        <uses-library
            android:name="android.ext.adservices"
            android:required="false"/>
        <activity
            android:name="com.vungle.ads.internal.ui.VungleActivity"
            android:launchMode="singleTop"
            android:configChanges="smallestScreenSize|screenSize|uiMode|screenLayout|orientation|keyboardHidden"
            android:hardwareAccelerated="true"/>
        <provider
            android:name="com.vungle.ads.VungleProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.vungle-provider"
            android:initOrder="102"/>
        <provider
            android:name="com.facebook.ads.AudienceNetworkContentProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.AudienceNetworkContentProvider"/>
        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false">
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct"/>
        </service>
        <activity
            android:theme="@style/a"
            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
            android:configChanges="screenSize|orientation|keyboardHidden"
            android:windowSoftInputMode="stateAlwaysHidden"/>
        <activity
            android:theme="@style/a"
            android:name="com.blankj.utilcode.util.UtilsTransActivity"
            android:multiprocess="true"
            android:configChanges="screenSize|orientation|keyboardHidden"
            android:windowSoftInputMode="stateAlwaysHidden"/>
        <provider
            android:name="com.blankj.utilcode.util.UtilsFileProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.utilcode.fileprovider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/n"/>
        </provider>
        <service
            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.bible.holy.bible.for.women.messenger"/>
            </intent-filter>
        </service>
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:permission="android.permission.DUMP"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="false">
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE"/>
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION"/>
            </intent-filter>
        </receiver>
        <activity
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen"
            android:name="com.pubmatic.sdk.webrendering.mraid.POBVideoPlayerActivity"
            android:screenOrientation="behind"
            android:configChanges="screenSize|orientation|keyboardHidden"/>
        <activity
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen"
            android:name="com.pubmatic.sdk.webrendering.ui.POBFullScreenActivity"
            android:configChanges="smallestScreenSize|screenSize|screenLayout|orientation|keyboardHidden"/>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="false"/>
        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false"/>
        <activity
            android:theme="@android:style/Theme.Black.NoTitleBar"
            android:name="com.pubmatic.sdk.common.browser.POBInternalBrowserActivity"
            android:configChanges="smallestScreenSize|screenSize|screenLayout|orientation|keyboardHidden"/>
        <provider
            android:name="com.adjust.sdk.SystemLifecycleContentProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.adjust-lifecycle-provider"/>
        <provider
            android:name="me.jessyan.autosize.InitProvider"
            android:exported="false"
            android:multiprocess="true"
            android:authorities="com.bible.holy.bible.for.women.autosize-init-provider"/>
        <activity
            android:theme="@style/ro"
            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
            android:exported="false"
            android:stateNotNeeded="true"/>
        <activity
            android:name="com.pairip.licensecheck.LicenseActivity"
            android:exported="false"/>
        <provider
            android:name="com.pairip.licensecheck.LicenseContentProvider"
            android:exported="false"
            android:authorities="com.bible.holy.bible.for.women.com.pairip.licensecheck.LicenseContentProvider"/>
        <meta-data
            android:name="com.android.vending.splits.required"
            android:value="true"/>
        <meta-data
            android:name="com.android.stamp.source"
            android:value="https://play.google.com/store"/>
        <meta-data
            android:name="com.android.stamp.type"
            android:value="STAMP_TYPE_DISTRIBUTION_APK"/>
        <meta-data
            android:name="com.android.vending.splits"
            android:resource="@xml/splits0"/>
        <meta-data
            android:name="com.android.vending.derived.apk.id"
            android:value="3"/>
    </application>
</manifest>
